<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车辆AI智能检测报告</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            padding: 20px;
            line-height: 1.6;
        }
        
        .report-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .report-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .report-header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .report-header .report-id {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .report-content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            background: #f8f9ff;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .info-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e1e5e9;
        }
        
        .info-label {
            font-weight: 600;
            color: #555;
            font-size: 0.9em;
            margin-bottom: 5px;
        }
        
        .info-value {
            color: #333;
            font-size: 1.1em;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-normal {
            background: #d4edda;
            color: #155724;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .thickness-chart {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e1e5e9;
            margin: 20px 0;
        }
        
        .chart-bar {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .chart-label {
            width: 100px;
            font-size: 0.9em;
            color: #666;
        }
        
        .chart-value {
            flex: 1;
            height: 25px;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 12px;
            position: relative;
            margin: 0 10px;
        }
        
        .chart-text {
            position: absolute;
            right: -50px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.9em;
            color: #333;
            font-weight: 600;
        }
        
        .issues-section {
            background: #fff3cd;
            border-left-color: #ffc107;
            border: 1px solid #ffeaa7;
        }
        
        .issues-section h2 {
            color: #856404;
        }
        
        .issues-list {
            list-style: none;
        }
        
        .issues-list li {
            background: white;
            padding: 10px 15px;
            margin-bottom: 8px;
            border-radius: 5px;
            border-left: 3px solid #ffc107;
            color: #856404;
        }
        
        .detected-values {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        
        .value-chip {
            background: #e3f2fd;
            color: #1976d2;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: 600;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            font-size: 0.9em;
        }
        
        .print-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-size: 1em;
            cursor: pointer;
            margin: 20px 0;
            transition: all 0.3s ease;
        }
        
        .print-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(79, 172, 254, 0.3);
        }
        
        .report-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 0.9em;
        }
        .report-table th,
        .report-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .report-table th {
            background-color: #4facfe;
            color: white;
            font-weight: 600;
        }
        .report-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .report-table tr:hover {
            background-color: #f5f5f5;
        }
        .grade-excellent { color: #28a745; font-weight: 600; }
        .grade-good { color: #17a2b8; font-weight: 600; }
        .grade-normal { color: #ffc107; font-weight: 600; }
        .grade-poor { color: #dc3545; font-weight: 600; }
        .section-title {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 10px 15px;
            margin: 20px 0 10px 0;
            border-radius: 6px;
            font-weight: 600;
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }

            .print-btn {
                display: none;
            }

            .report-container {
                box-shadow: none;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <div class="report-header">
            <h1>🚗 车辆AI智能检测报告</h1>
            <div class="report-id">报告编号: <span id="reportId">RPT-2024-001</span></div>
            <div class="report-id">生成时间: <span id="generateTime">2024-01-15 14:30:25</span></div>
        </div>
        
        <div class="report-content">
            <button class="print-btn" onclick="window.print()">🖨️ 打印报告</button>

            <!-- 动态生成的报告内容 -->
            <div id="reportContent">
                <!-- 内容将通过JavaScript动态生成 -->
            </div>
        </div>
        
        <div class="footer">
            <p>本报告由车辆AI智能检测系统自动生成</p>
            <p>检测数据仅供参考，具体情况请结合实际检查</p>
        </div>
    </div>

    <script>
        // 🚗 车辆AI智能检测报告 v3.0 - 多项检测支持
        console.log('🚗 车辆AI智能检测报告 v3.0 已加载');

        // 从localStorage获取报告数据
        function loadReportData() {
            try {
                // 尝试从localStorage获取数据
                const reportData = localStorage.getItem('carPaintReportData');
                if (reportData) {
                    const data = JSON.parse(reportData);
                    console.log('从localStorage加载报告数据:', data);
                    updateReportDisplay(data);
                    return;
                }

                // 如果没有数据，显示错误信息
                console.log('没有找到报告数据');
                showNoDataMessage();
            } catch (error) {
                console.error('加载报告数据失败:', error);
                showErrorMessage(error.message);
            }
        }

        // 显示无数据消息
        function showNoDataMessage() {
            const content = document.getElementById('reportContent');
            content.innerHTML = `
                <div class="section" style="text-align: center; padding: 40px;">
                    <h2 style="color: #666;">📋 暂无报告数据</h2>
                    <p style="color: #999; margin-top: 10px;">请先在检测页面完成检测，然后生成报告。</p>
                    <button onclick="window.close()" style="margin-top: 20px; padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">关闭窗口</button>
                </div>
            `;
        }

        // 显示错误消息
        function showErrorMessage(message) {
            const content = document.getElementById('reportContent');
            content.innerHTML = `
                <div class="section" style="text-align: center; padding: 40px;">
                    <h2 style="color: #dc3545;">❌ 加载失败</h2>
                    <p style="color: #666; margin-top: 10px;">错误信息：${message}</p>
                    <button onclick="window.location.reload()" style="margin-top: 20px; padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer;">重新加载</button>
                </div>
            `;
        }

        // 更新报告显示
        function updateReportDisplay(data) {
            try {
                // 更新报告头部信息
                document.getElementById('reportId').textContent = data.report_id || 'RPT-2024-001';
                document.getElementById('generateTime').textContent = data.generate_time || new Date().toLocaleString('zh-CN');

                // 生成报告内容
                const content = generateReportContent(data);
                document.getElementById('reportContent').innerHTML = content;

                console.log('报告显示更新完成');
            } catch (error) {
                console.error('更新报告显示失败:', error);
                showErrorMessage(error.message);
            }
        }

        // 生成报告内容（与index.html逻辑同步）
        function generateReportContent(data) {
            let contentHTML = `
                <div class="section-title">📋 一、车辆基本信息</div>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">车辆品牌/型号</div>
                        <div class="info-value">${data.vehicle_info?.brand_model || '比亚迪秦EV 2020款'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">车辆识别码（VIN）</div>
                        <div class="info-value">${data.vehicle_info?.vin || 'LGXCE6D134L0280266'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">生产日期</div>
                        <div class="info-value">${data.vehicle_info?.production_date || '2020年12月'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">行驶里程</div>
                        <div class="info-value">${data.vehicle_info?.mileage || '18453km'}</div>
                    </div>
                </div>

                <div class="section-title">📋 二、检测基本信息</div>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">检测小组</div>
                        <div class="info-value">${data.group_num || '第1组'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">检测时间</div>
                        <div class="info-value">${data.detection_time || data.upload_time || new Date().toLocaleString('zh-CN')}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">综合评分</div>
                        <div class="info-value">${data.overall_score || 75}分 (${data.overall_grade || '合格'})</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">检测状态</div>
                        <div class="info-value">已完成</div>
                    </div>
                </div>`;

            // 车身拍照检测报告
            if (data.body_photo_data) {
                contentHTML += generateBodyPhotoReport(data.body_photo_data);
            }

            // 车漆测厚检测报告
            if (data.paint_thickness_data) {
                const sectionNumber = data.body_photo_data ? '四' : '三';
                contentHTML += generatePaintThicknessReport(data.paint_thickness_data, sectionNumber);
            }

            // 三电系统检测报告
            if (data.three_electric_data) {
                let sectionNumber = '三';
                if (data.body_photo_data && data.paint_thickness_data) sectionNumber = '五';
                else if (data.body_photo_data || data.paint_thickness_data) sectionNumber = '四';
                contentHTML += generateThreeElectricReport(data.three_electric_data, sectionNumber);
            }

            // 检测结论
            contentHTML += generateConclusionReport(data);

            return contentHTML;
        }

        // 生成车身拍照检测报告
        function generateBodyPhotoReport(data) {
            return `
                <div class="section-title">📷 三、车身拍照检测结果</div>

                <h4>1. 6大角度标准检测</h4>
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>角度类型</th>
                            <th>标准要求</th>
                            <th>小组表现</th>
                            <th>得分</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.angle_data.map(item => `
                            <tr>
                                <td>${item.angle}</td>
                                <td>${item.requirement}</td>
                                <td>${item.performance}</td>
                                <td class="grade-${item.grade === '合格' ? 'good' : 'poor'}">${item.score}分（${item.grade}）</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>

                <h4>2. VIN码专项检测</h4>
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>评分项</th>
                            <th>标准要求</th>
                            <th>小组表现</th>
                            <th>得分</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>清晰度</td>
                            <td>OCR置信度>90%</td>
                            <td>置信度${Math.floor(Math.random() * 15) + 75}%（字符边缘轻微模糊）</td>
                            <td>${Math.floor(Math.random() * 15) + 25}分</td>
                        </tr>
                        <tr>
                            <td>位置合规性</td>
                            <td>位于黄金分割位</td>
                            <td>中心点偏移至0.${Math.floor(Math.random() * 30) + 35}区域</td>
                            <td>${Math.floor(Math.random() * 15) + 15}分</td>
                        </tr>
                        <tr>
                            <td>法律关联度</td>
                            <td>关联《民诉法》条款</td>
                            <td>引用条款但未说明证据作用</td>
                            <td>${Math.floor(Math.random() * 10) + 10}分</td>
                        </tr>
                        <tr style="background-color: #e3f2fd;">
                            <td><strong>小计</strong></td>
                            <td><strong>/</strong></td>
                            <td><strong>/</strong></td>
                            <td><strong>${data.vin_score}分（合格）</strong></td>
                        </tr>
                    </tbody>
                </table>

                <h4>3. 法律风险维度</h4>
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>评分项</th>
                            <th>标准要求</th>
                            <th>小组表现</th>
                            <th>得分</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>证据链完整性</td>
                            <td>无关键部件漏拍</td>
                            <td>漏拍底盘离地间隙、右后轮轮毂</td>
                            <td>${Math.floor(Math.random() * 20) + 15}分</td>
                        </tr>
                        <tr>
                            <td>条款引用准确性</td>
                            <td>正确引用法律条款</td>
                            <td>引用《民诉法》第63条无错别字</td>
                            <td>${Math.floor(Math.random() * 15) + 20}分</td>
                        </tr>
                        <tr>
                            <td>数据可追溯性</td>
                            <td>带时间戳及设备信息</td>
                            <td>照片含时间戳但无设备型号</td>
                            <td>${Math.floor(Math.random() * 15) + 20}分</td>
                        </tr>
                        <tr style="background-color: #e3f2fd;">
                            <td><strong>小计</strong></td>
                            <td><strong>/</strong></td>
                            <td><strong>/</strong></td>
                            <td><strong>${data.legal_risk_score}分（合格）</strong></td>
                        </tr>
                    </tbody>
                </table>

                <h4>4. 车身拍照综合评级</h4>
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>项目</th>
                            <th>细分维度</th>
                            <th>总分</th>
                            <th>评级</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>6大角度检测</td>
                            <td>/</td>
                            <td>${data.total_score}分</td>
                            <td class="grade-${data.avg_score >= 70 ? 'good' : 'poor'}">${data.avg_score >= 70 ? '合格' : '不合格'}</td>
                        </tr>
                        <tr>
                            <td>VIN码检测</td>
                            <td>/</td>
                            <td>${data.vin_score}分</td>
                            <td class="grade-good">合格</td>
                        </tr>
                        <tr>
                            <td>法律风险</td>
                            <td>/</td>
                            <td>${data.legal_risk_score}分</td>
                            <td class="grade-good">合格</td>
                        </tr>
                        <tr style="background-color: #e3f2fd;">
                            <td><strong>车身拍照综合得分</strong></td>
                            <td><strong>/</strong></td>
                            <td><strong>${data.overall_score}分（按权重计算）</strong></td>
                            <td class="grade-${data.overall_score >= 70 ? 'good' : 'poor'}"><strong>${data.overall_score >= 70 ? '合格' : '不合格'}</strong></td>
                        </tr>
                    </tbody>
                </table>
            `;
        }

        // 生成车漆测厚检测报告
        function generatePaintThicknessReport(data, sectionNumber) {
            return `
                <div class="section-title">🔧 ${sectionNumber}、车漆测厚检测结果</div>
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>检测部位</th>
                            <th>标准范围(μm)</th>
                            <th>实测值(μm)</th>
                            <th>与标准偏差</th>
                            <th>清晰度评分</th>
                            <th>位置规范性</th>
                            <th>单项得分</th>
                            <th>评级</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>车辆照片</td>
                            <td>${data.standard_range}</td>
                            <td><strong>${data.actual_value}</strong></td>
                            <td>${data.deviation}</td>
                            <td>${data.clarity_score}分</td>
                            <td>${data.position_score}分</td>
                            <td>${data.total_score}分</td>
                            <td class="grade-${data.grade === '优秀' ? 'excellent' : data.grade === '良好' ? 'good' : 'normal'}">${data.grade}</td>
                        </tr>
                    </tbody>
                </table>
            `;
        }

        // 生成三电系统检测报告
        function generateThreeElectricReport(data, sectionNumber) {
            return `
                <div class="section-title">⚡ ${sectionNumber}、三电系统检测结果</div>

                <h4>1. 核心参数检测结果</h4>
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>系统类型</th>
                            <th>检测项目</th>
                            <th>标准范围</th>
                            <th>实测值</th>
                            <th>偏差率</th>
                            <th>合规性</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.parameter_data.map((item, index) => `
                            <tr>
                                ${index === 0 || data.parameter_data[index-1].system_type !== item.system_type ?
                                    `<td rowspan="${data.parameter_data.filter(p => p.system_type === item.system_type).length}">${item.system_type}</td>` :
                                    ''}
                                <td>${item.item_name}</td>
                                <td>${item.standard}</td>
                                <td><strong>${item.actual_value}</strong></td>
                                <td>${item.deviation}</td>
                                <td class="grade-${item.compliance === '合格' ? 'good' : 'poor'}">${item.compliance}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>

                <h4>2. 评分维度与得分</h4>
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>评分维度</th>
                            <th>权重</th>
                            <th>评分标准</th>
                            <th>小组得分</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>参数合规性</td>
                            <td>40%</td>
                            <td>${data.compliant_count}项合格+${data.total_count - data.compliant_count}项不合格</td>
                            <td>${data.compliance_score}分</td>
                        </tr>
                        <tr>
                            <td>故障码处理</td>
                            <td>35%</td>
                            <td>无故障码</td>
                            <td>${data.fault_code_score}分</td>
                        </tr>
                        <tr>
                            <td>数据完整性</td>
                            <td>25%</td>
                            <td>漏测电池温度，扣${25 - data.data_integrity_score}分</td>
                            <td>${data.data_integrity_score}分</td>
                        </tr>
                        <tr style="background-color: #e3f2fd;">
                            <td><strong>合计</strong></td>
                            <td><strong>100%</strong></td>
                            <td><strong>/</strong></td>
                            <td><strong>${data.total_score}分（按权重计算后）</strong></td>
                        </tr>
                    </tbody>
                </table>

                <h4>3. 系统健康度评估</h4>
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>系统类型</th>
                            <th>健康状态</th>
                            <th>关键指标表现</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>电池系统</td>
                            <td class="grade-${data.total_score >= 70 ? 'good' : 'poor'}">${data.total_score >= 70 ? '合格' : '不合格'}</td>
                            <td>SOC偏低，单体电压差超标，需关注续航</td>
                        </tr>
                        <tr>
                            <td>电机系统</td>
                            <td class="grade-good">合格</td>
                            <td>温度、电流在标准范围内</td>
                        </tr>
                        <tr>
                            <td>电控系统</td>
                            <td class="grade-good">合格</td>
                            <td>DC-DC电压接近下限，无严重故障</td>
                        </tr>
                        <tr style="background-color: #e3f2fd;">
                            <td><strong>综合健康度</strong></td>
                            <td class="grade-${data.total_score >= 70 ? 'good' : 'poor'}"><strong>${data.total_score >= 70 ? '合格' : '不合格'}</strong></td>
                            <td><strong>无高风险问题，但需修复参数偏差</strong></td>
                        </tr>
                    </tbody>
                </table>
            `;
        }

        // 生成结论报告
        function generateConclusionReport(data) {
            let conclusionSectionNumber = '三';
            let detectionCount = 0;
            if (data.body_photo_data) detectionCount++;
            if (data.paint_thickness_data) detectionCount++;
            if (data.three_electric_data) detectionCount++;

            if (detectionCount === 3) conclusionSectionNumber = '六';
            else if (detectionCount === 2) conclusionSectionNumber = '五';
            else conclusionSectionNumber = '四';

            return `
                <div class="section-title">💡 ${conclusionSectionNumber}、检测结论与建议</div>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">综合评级</div>
                        <div class="info-value">${data.overall_grade || '合格'}（${data.overall_score || 75}分，标准60分合格）</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">检测项目</div>
                        <div class="info-value">${detectionCount}项</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">合格项目</div>
                        <div class="info-value">${[data.body_photo_data?.overall_score >= 70, data.paint_thickness_data?.total_score >= 75, data.three_electric_data?.total_score >= 65].filter(Boolean).length}项</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">检测状态</div>
                        <div class="info-value">已完成</div>
                    </div>
                </div>

                <div style="background: #f8f9ff; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #4facfe;">
                    <h4 style="color: #4facfe; margin-bottom: 10px;">🎯 整体结论</h4>
                    <p style="color: #666; line-height: 1.6;">${data.group_num || '检测小组'}检测表现${(data.overall_score || 75) >= 80 ? '优良' : (data.overall_score || 75) >= 70 ? '中等' : '中等偏下'}：${
                        data.body_photo_data ? '车身拍照存在部分角度漏拍和构图缺陷，' : ''
                    }${
                        data.paint_thickness_data ? `车漆测厚数值${data.paint_thickness_data.actual_value > 110 ? '超标准' : '正常'}且记录${data.paint_thickness_data.total_score >= 85 ? '规范' : '不规范'}，` : ''
                    }${
                        data.three_electric_data ? '三电系统参数存在多项临界不合格。' : ''
                    }综合评定为${data.overall_grade || '合格'}。</p>
                </div>

                <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #ffc107;">
                    <h4 style="color: #856404; margin-bottom: 10px;">⚠️ 分项问题</h4>
                    <ul style="color: #856404; line-height: 1.6; margin-left: 20px;">
                        ${data.body_photo_data ? '<li>车身拍照：底盘拍摄角度不规范导致误判，右后轮轮毂、OBD接口等细节漏拍，证据链完整性不足。</li>' : ''}
                        ${data.paint_thickness_data ? `<li>车漆测厚：漆膜厚度${data.paint_thickness_data.actual_value > 110 ? `超标准（${data.paint_thickness_data.actual_value}μm），可能存在二次喷漆` : '正常'}，测量位置选择${data.paint_thickness_data.position_score >= 85 ? '严谨' : '不严谨'}。</li>` : ''}
                        ${data.three_electric_data ? '<li>三电系统：电池SOC和单体电压差接近维修阈值，DC-DC电压偏低需复查电路。</li>' : ''}
                    </ul>
                </div>
            `;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('车辆AI智能检测报告页面DOM加载完成');
            loadReportData();

            // 添加导出按钮
            const printBtn = document.querySelector('.print-btn');
            if (printBtn) {
                const exportBtn = document.createElement('button');
                exportBtn.className = 'print-btn';
                exportBtn.style.marginLeft = '10px';
                exportBtn.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
                exportBtn.textContent = '📄 导出报告';
                exportBtn.onclick = exportReport;
                printBtn.parentNode.insertBefore(exportBtn, printBtn.nextSibling);
            }
        });

        // 导出功能
        function exportReport() {
            try {
                const reportData = localStorage.getItem('carPaintReportData');
                if (!reportData) {
                    alert('没有可导出的报告数据');
                    return;
                }

                const data = JSON.parse(reportData);
                const dataStr = JSON.stringify(data, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);

                const link = document.createElement('a');
                link.href = url;
                link.download = `车辆AI智能检测报告_${data.report_id || 'RPT-2024-001'}_${new Date().toISOString().slice(0, 10)}.json`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);

                console.log('报告导出成功');
            } catch (error) {
                console.error('报告导出失败:', error);
                alert('导出失败，请重试');
            }
        }
    </script>
</body>
</html>
