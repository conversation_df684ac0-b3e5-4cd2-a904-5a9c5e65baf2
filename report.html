<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车漆漆膜检测报告</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            padding: 20px;
            line-height: 1.6;
        }
        
        .report-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .report-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .report-header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .report-header .report-id {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .report-content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            background: #f8f9ff;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .info-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e1e5e9;
        }
        
        .info-label {
            font-weight: 600;
            color: #555;
            font-size: 0.9em;
            margin-bottom: 5px;
        }
        
        .info-value {
            color: #333;
            font-size: 1.1em;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-normal {
            background: #d4edda;
            color: #155724;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .thickness-chart {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e1e5e9;
            margin: 20px 0;
        }
        
        .chart-bar {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .chart-label {
            width: 100px;
            font-size: 0.9em;
            color: #666;
        }
        
        .chart-value {
            flex: 1;
            height: 25px;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 12px;
            position: relative;
            margin: 0 10px;
        }
        
        .chart-text {
            position: absolute;
            right: -50px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.9em;
            color: #333;
            font-weight: 600;
        }
        
        .issues-section {
            background: #fff3cd;
            border-left-color: #ffc107;
            border: 1px solid #ffeaa7;
        }
        
        .issues-section h2 {
            color: #856404;
        }
        
        .issues-list {
            list-style: none;
        }
        
        .issues-list li {
            background: white;
            padding: 10px 15px;
            margin-bottom: 8px;
            border-radius: 5px;
            border-left: 3px solid #ffc107;
            color: #856404;
        }
        
        .detected-values {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        
        .value-chip {
            background: #e3f2fd;
            color: #1976d2;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: 600;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            font-size: 0.9em;
        }
        
        .print-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-size: 1em;
            cursor: pointer;
            margin: 20px 0;
            transition: all 0.3s ease;
        }
        
        .print-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(79, 172, 254, 0.3);
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .print-btn {
                display: none;
            }
            
            .report-container {
                box-shadow: none;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <div class="report-header">
            <h1>🚗 车漆漆膜检测报告</h1>
            <div class="report-id">报告编号: <span id="reportId">RPT-2024-001</span></div>
            <div class="report-id">生成时间: <span id="generateTime">2024-01-15 14:30:25</span></div>
        </div>
        
        <div class="report-content">
            <button class="print-btn" onclick="window.print()">🖨️ 打印报告</button>
            
            <!-- 基本信息 -->
            <div class="section">
                <h2>📋 基本信息</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">小组编号</div>
                        <div class="info-value" id="groupNum">第1组</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">检测部位</div>
                        <div class="info-value" id="carPart">前门</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">检测时间</div>
                        <div class="info-value" id="uploadTime">2024-01-15 14:30:25</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">检测状态</div>
                        <div class="info-value">
                            <span class="status-indicator status-normal" id="resultStatus">正常</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 检测结果 -->
            <div class="section">
                <h2>📊 检测结果</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">平均厚度</div>
                        <div class="info-value"><span id="avgThickness">105</span> μm</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">最大厚度</div>
                        <div class="info-value"><span id="maxThickness">120</span> μm</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">最小厚度</div>
                        <div class="info-value"><span id="minThickness">95</span> μm</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">厚度差异</div>
                        <div class="info-value"><span id="thicknessDiff">25</span> μm</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">标准范围</div>
                        <div class="info-value" id="standardRange">90-110 μm</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">检测点数</div>
                        <div class="info-value"><span id="detectionCount">6</span> 个</div>
                    </div>
                </div>
                
                <!-- 厚度对比图表 -->
                <div class="thickness-chart">
                    <h3 style="margin-bottom: 15px; color: #333;">厚度对比</h3>
                    <div class="chart-bar">
                        <div class="chart-label">平均值</div>
                        <div class="chart-value" id="avgChart" style="width: 52.5%;">
                            <div class="chart-text">105μm</div>
                        </div>
                    </div>
                    <div class="chart-bar">
                        <div class="chart-label">最大值</div>
                        <div class="chart-value" id="maxChart" style="width: 60%;">
                            <div class="chart-text">120μm</div>
                        </div>
                    </div>
                    <div class="chart-bar">
                        <div class="chart-label">最小值</div>
                        <div class="chart-value" id="minChart" style="width: 47.5%;">
                            <div class="chart-text">95μm</div>
                        </div>
                    </div>
                </div>
                
                <!-- 检测到的数值 -->
                <div>
                    <h3 style="margin-bottom: 10px; color: #333;">检测到的厚度值</h3>
                    <div class="detected-values" id="detectedValues">
                        <span class="value-chip">105μm</span>
                        <span class="value-chip">120μm</span>
                        <span class="value-chip">95μm</span>
                        <span class="value-chip">110μm</span>
                        <span class="value-chip">98μm</span>
                        <span class="value-chip">102μm</span>
                    </div>
                </div>
            </div>

            <!-- 问题分析 -->
            <div class="section issues-section" id="issuesSection" style="display: none;">
                <h2>⚠️ 问题分析</h2>
                <ul class="issues-list" id="issuesList">
                    <!-- 动态生成问题列表 -->
                </ul>
            </div>
            
            <!-- 结论建议 -->
            <div class="section">
                <h2>💡 结论与建议</h2>
                <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e1e5e9;" id="conclusionSection">
                    <p style="color: #28a745; font-weight: 600;" id="conclusionTitle">✅ 检测结果正常</p>
                    <p style="margin-top: 10px; color: #666;" id="conclusionText">
                        该部位的漆膜厚度在标准范围内，厚度分布均匀，未发现明显的修补痕迹。
                    </p>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>本报告由车漆漆膜检测系统自动生成</p>
            <p>检测数据仅供参考，具体情况请结合实际检查</p>
        </div>
    </div>

    <script>
        // 🚗 车漆检测报告 v2.1 - 动态数据支持
        console.log('🚗 车漆检测报告 v2.1 已加载');

        // 从URL参数或localStorage获取报告数据
        function loadReportData() {
            try {
                // 尝试从localStorage获取数据
                const reportData = localStorage.getItem('carPaintReportData');
                if (reportData) {
                    const data = JSON.parse(reportData);
                    console.log('从localStorage加载报告数据:', data);
                    updateReportDisplay(data);
                    return;
                }

                // 如果没有数据，使用默认示例数据
                console.log('使用默认示例数据');
                const defaultData = {
                    report_id: 'RPT-2024-001',
                    generate_time: new Date().toLocaleString('zh-CN'),
                    group_num: '第1组',
                    car_part: '前门',
                    upload_time: new Date().toLocaleString('zh-CN'),
                    result_status: 'normal',
                    avg_thickness: 105,
                    max_thickness: 120,
                    min_thickness: 95,
                    thickness_diff: 25,
                    standard_range: '90-110 μm',
                    detection_count: 6,
                    detected_values: [105, 120, 95, 110, 98, 102],
                    issues: []
                };
                updateReportDisplay(defaultData);
            } catch (error) {
                console.error('加载报告数据失败:', error);
            }
        }

        // 更新报告显示
        function updateReportDisplay(data) {
            try {
                // 更新基本信息
                document.getElementById('reportId').textContent = data.report_id || 'RPT-2024-001';
                document.getElementById('generateTime').textContent = data.generate_time || new Date().toLocaleString('zh-CN');
                document.getElementById('groupNum').textContent = data.group_num || '第1组';
                document.getElementById('carPart').textContent = data.car_part || '前门';
                document.getElementById('uploadTime').textContent = data.upload_time || new Date().toLocaleString('zh-CN');

                // 更新检测结果
                document.getElementById('avgThickness').textContent = data.avg_thickness || 105;
                document.getElementById('maxThickness').textContent = data.max_thickness || 120;
                document.getElementById('minThickness').textContent = data.min_thickness || 95;
                document.getElementById('thicknessDiff').textContent = data.thickness_diff || 25;
                document.getElementById('standardRange').textContent = data.standard_range || '90-110 μm';
                document.getElementById('detectionCount').textContent = data.detection_count || 6;

                // 更新状态指示器
                updateStatusIndicator(data.result_status || 'normal');

                // 更新图表
                updateCharts(data);

                // 更新检测数值
                updateDetectedValues(data.detected_values || [105, 120, 95, 110, 98, 102]);

                // 更新问题分析
                updateIssuesSection(data.issues || []);

                // 更新结论建议
                updateConclusion(data.result_status || 'normal');

                console.log('报告显示更新完成');
            } catch (error) {
                console.error('更新报告显示失败:', error);
            }
        }

        // 更新状态指示器
        function updateStatusIndicator(status) {
            const statusElement = document.getElementById('resultStatus');
            statusElement.className = 'status-indicator';

            switch (status) {
                case 'normal':
                    statusElement.classList.add('status-normal');
                    statusElement.textContent = '正常';
                    break;
                case 'thin':
                case 'thick':
                case 'uneven':
                    statusElement.classList.add('status-warning');
                    statusElement.textContent = '异常';
                    break;
                default:
                    statusElement.classList.add('status-error');
                    statusElement.textContent = '错误';
            }
        }

        // 更新图表
        function updateCharts(data) {
            const maxValue = 200; // 图表最大值

            // 更新平均值图表
            const avgChart = document.getElementById('avgChart');
            const avgWidth = Math.min((data.avg_thickness / maxValue) * 100, 100);
            avgChart.style.width = avgWidth + '%';
            avgChart.querySelector('.chart-text').textContent = data.avg_thickness + 'μm';

            // 更新最大值图表
            const maxChart = document.getElementById('maxChart');
            const maxWidth = Math.min((data.max_thickness / maxValue) * 100, 100);
            maxChart.style.width = maxWidth + '%';
            maxChart.querySelector('.chart-text').textContent = data.max_thickness + 'μm';

            // 更新最小值图表
            const minChart = document.getElementById('minChart');
            const minWidth = Math.min((data.min_thickness / maxValue) * 100, 100);
            minChart.style.width = minWidth + '%';
            minChart.querySelector('.chart-text').textContent = data.min_thickness + 'μm';
        }

        // 更新检测数值
        function updateDetectedValues(values) {
            const container = document.getElementById('detectedValues');
            container.innerHTML = '';

            values.forEach(value => {
                const chip = document.createElement('span');
                chip.className = 'value-chip';
                chip.textContent = value + 'μm';
                container.appendChild(chip);
            });
        }

        // 更新问题分析
        function updateIssuesSection(issues) {
            const section = document.getElementById('issuesSection');
            const list = document.getElementById('issuesList');

            if (issues && issues.length > 0) {
                section.style.display = 'block';
                list.innerHTML = '';

                issues.forEach(issue => {
                    const li = document.createElement('li');
                    li.textContent = issue;
                    list.appendChild(li);
                });
            } else {
                section.style.display = 'none';
            }
        }

        // 更新结论建议
        function updateConclusion(status) {
            const titleElement = document.getElementById('conclusionTitle');
            const textElement = document.getElementById('conclusionText');

            switch (status) {
                case 'normal':
                    titleElement.style.color = '#28a745';
                    titleElement.textContent = '✅ 检测结果正常';
                    textElement.textContent = '该部位的漆膜厚度在标准范围内，厚度分布均匀，未发现明显的修补痕迹。';
                    break;
                case 'thin':
                    titleElement.style.color = '#ffc107';
                    titleElement.textContent = '⚠️ 漆膜厚度偏薄';
                    textElement.textContent = '该部位的漆膜厚度低于标准范围，可能存在原厂漆面磨损或重新喷漆后厚度不足的情况。';
                    break;
                case 'thick':
                    titleElement.style.color = '#ffc107';
                    titleElement.textContent = '⚠️ 漆膜厚度偏厚';
                    textElement.textContent = '该部位的漆膜厚度高于标准范围，可能存在重新喷漆或多次修补的情况。';
                    break;
                case 'uneven':
                    titleElement.style.color = '#ffc107';
                    titleElement.textContent = '⚠️ 漆膜厚度不均';
                    textElement.textContent = '该部位的漆膜厚度差异较大，可能存在局部修补或喷漆工艺不均匀的情况。';
                    break;
                default:
                    titleElement.style.color = '#dc3545';
                    titleElement.textContent = '❌ 检测异常';
                    textElement.textContent = '检测过程中发生异常，请重新进行检测。';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('报告页面DOM加载完成');
            loadReportData();
        });

        // 导出功能
        function exportReport() {
            try {
                // 创建导出数据
                const reportData = {
                    report_id: document.getElementById('reportId').textContent,
                    generate_time: document.getElementById('generateTime').textContent,
                    group_num: document.getElementById('groupNum').textContent,
                    car_part: document.getElementById('carPart').textContent,
                    upload_time: document.getElementById('uploadTime').textContent,
                    avg_thickness: document.getElementById('avgThickness').textContent,
                    max_thickness: document.getElementById('maxThickness').textContent,
                    min_thickness: document.getElementById('minThickness').textContent,
                    thickness_diff: document.getElementById('thicknessDiff').textContent,
                    standard_range: document.getElementById('standardRange').textContent,
                    detection_count: document.getElementById('detectionCount').textContent
                };

                // 创建下载链接
                const dataStr = JSON.stringify(reportData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);

                const link = document.createElement('a');
                link.href = url;
                link.download = `车漆检测报告_${reportData.report_id}_${new Date().toISOString().slice(0, 10)}.json`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);

                console.log('报告导出成功');
            } catch (error) {
                console.error('报告导出失败:', error);
                alert('导出失败，请重试');
            }
        }

        // 添加导出按钮到打印按钮旁边
        document.addEventListener('DOMContentLoaded', function() {
            const printBtn = document.querySelector('.print-btn');
            if (printBtn) {
                const exportBtn = document.createElement('button');
                exportBtn.className = 'print-btn';
                exportBtn.style.marginLeft = '10px';
                exportBtn.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
                exportBtn.textContent = '📄 导出报告';
                exportBtn.onclick = exportReport;
                printBtn.parentNode.insertBefore(exportBtn, printBtn.nextSibling);
            }
        });
    </script>
</body>
</html>
