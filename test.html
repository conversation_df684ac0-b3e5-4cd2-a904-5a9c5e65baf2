<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .upload-sections {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .part-upload-section {
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 15px;
            background: #f8f9ff;
        }
        .part-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        .part-upload-area {
            border: 2px dashed #4facfe;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: white;
            cursor: pointer;
            min-height: 120px;
        }
        .part-upload-area:hover {
            border-color: #00f2fe;
            background: #f0f8ff;
        }
    </style>
</head>
<body>
    <h1>车漆检测系统测试</h1>
    
    <div class="upload-sections" id="uploadSections">
        <!-- 这里会动态生成上传区域 -->
    </div>
    
    <div>
        <p>上传进度: <span id="progress">0/8</span></p>
        <button id="testBtn">开始检测</button>
    </div>

    <script>
        console.log('测试页面加载');
        
        // 车辆部位配置
        const carParts = [
            { id: 'front-bumper', name: '前保险杠', icon: '🚗' },
            { id: 'rear-bumper', name: '后保险杠', icon: '🚙' },
            { id: 'front-fender', name: '前翼子板', icon: '🚘' },
            { id: 'rear-fender', name: '后翼子板', icon: '🚖' },
            { id: 'door', name: '车门', icon: '🚪' },
            { id: 'hood', name: '引擎盖', icon: '🔧' },
            { id: 'roof', name: '车顶', icon: '🏠' },
            { id: 'trunk', name: '后备箱盖', icon: '📦' }
        ];
        
        function createUploadSection(part) {
            const section = document.createElement('div');
            section.className = 'part-upload-section';
            section.innerHTML = `
                <div class="part-title">${part.icon} ${part.name}</div>
                <div class="part-upload-area" onclick="alert('点击了${part.name}')">
                    <div>📷</div>
                    <div>点击上传图片</div>
                    <input type="file" style="display: none;" accept="image/*">
                </div>
            `;
            return section;
        }
        
        function init() {
            console.log('开始初始化');
            const container = document.getElementById('uploadSections');
            
            carParts.forEach(part => {
                const section = createUploadSection(part);
                container.appendChild(section);
                console.log('添加了部位:', part.name);
            });
            
            console.log('初始化完成');
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
